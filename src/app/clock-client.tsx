"use client";

import { useState, useEffect } from "react";
import { DigitalClock } from "@/components/time/digital-clock";
import { ClockSettings } from "@/components/time/clock-settings";

// Simple localStorage utilities
const STORAGE_KEY = "clockSettings";

interface ClockSettings {
  showSeconds: boolean;
  showWeekday: boolean;
  showDate: boolean;
  showWeekNumber: boolean;
  use12Hours: boolean;
  textColor: string;
  fontSize: number;
  fontFamily: string;
  position: { x: number; y: number };
  backgroundColor: string;
  backgroundImage: string;
}

const defaultSettings: ClockSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: false,
  use12Hours: false,
  textColor: "#ffffff",
  fontSize: 4,
  fontFamily: "monospace",
  position: { x: 50, y: 50 },
  backgroundColor: "#000000",
  backgroundImage: "",
};

function loadSettings(): ClockSettings {
  if (typeof window === "undefined") return defaultSettings;
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? { ...defaultSettings, ...JSON.parse(stored) } : defaultSettings;
  } catch {
    return defaultSettings;
  }
}

function saveSettings(settings: ClockSettings) {
  if (typeof window === "undefined") return;
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
  } catch {
    // Ignore storage errors
  }
}

export function ClockClient() {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showSeconds, setShowSeconds] = useState(defaultSettings.showSeconds);
  const [showWeekday, setShowWeekday] = useState(defaultSettings.showWeekday);
  const [showDate, setShowDate] = useState(defaultSettings.showDate);
  const [showWeekNumber, setShowWeekNumber] = useState(defaultSettings.showWeekNumber);
  const [use12Hours, setUse12Hours] = useState(defaultSettings.use12Hours);
  const [textColor, setTextColor] = useState(defaultSettings.textColor);
  const [fontSize, setFontSize] = useState(defaultSettings.fontSize);
  const [fontFamily, setFontFamily] = useState(defaultSettings.fontFamily);
  const [position, setPosition] = useState(defaultSettings.position);
  const [backgroundColor, setBackgroundColor] = useState(defaultSettings.backgroundColor);
  const [backgroundImage, setBackgroundImage] = useState(defaultSettings.backgroundImage);

  // Load settings on mount
  useEffect(() => {
    const settings = loadSettings();
    setShowSeconds(settings.showSeconds);
    setShowWeekday(settings.showWeekday);
    setShowDate(settings.showDate);
    setShowWeekNumber(settings.showWeekNumber);
    setUse12Hours(settings.use12Hours);
    setTextColor(settings.textColor);
    setFontSize(settings.fontSize);
    setFontFamily(settings.fontFamily);
    setPosition(settings.position);
    setBackgroundColor(settings.backgroundColor);
    setBackgroundImage(settings.backgroundImage);
  }, []);

  // Save settings when they change
  useEffect(() => {
    const settings: ClockSettings = {
      showSeconds,
      showWeekday,
      showDate,
      showWeekNumber,
      use12Hours,
      textColor,
      fontSize,
      fontFamily,
      position,
      backgroundColor,
      backgroundImage,
    };
    saveSettings(settings);
  }, [
    showSeconds,
    showWeekday,
    showDate,
    showWeekNumber,
    use12Hours,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage,
  ]);

  // Fullscreen handling
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  const backgroundStyle = backgroundImage
    ? {
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
      }
    : { backgroundColor };

  return (
    <>
      {isFullScreen ? (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={backgroundStyle}
        >
          <DigitalClock
            showSeconds={showSeconds}
            showWeekday={showWeekday}
            showDate={showDate}
            showWeekNumber={showWeekNumber}
            use12Hours={use12Hours}
            textColor={textColor}
            fontSize={fontSize}
            fontFamily={fontFamily}
            position={position}
          />
        </div>
      ) : (
        <>
          <div
            className="flex-1 flex items-center justify-center relative"
            style={backgroundStyle}
          >
            <DigitalClock
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 md:top-20 md:bottom-auto md:right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>
        </>
      )}
    </>
  );
}
