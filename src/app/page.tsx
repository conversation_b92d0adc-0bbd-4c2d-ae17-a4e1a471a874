"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { DigitalClock } from "@/components/time/digital-clock";
import { ClockSettings } from "@/components/time/clock-settings";
import { SEOContent } from "@/components/seo/seo-content";
import { StructuredData } from "@/components/seo/structured-data";

// Simple localStorage utilities
const STORAGE_KEY = "clockSettings";

const defaultSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: true,
  use12Hours: false,
  textColor: "#000000",
  fontSize: "6rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

export default function Home() {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load settings on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Monitor fullscreen state (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [isMounted]);

  // Save settings when they change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Error saving settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Extract settings for easier access
  const {
    showSeconds,
    showWeekday,
    showDate,
    showWeekNumber,
    use12Hours,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage,
  } = settings;

  // Update setting function
  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Individual setter functions
  const setShowSeconds = (value: boolean) => updateSetting('showSeconds', value);
  const setShowWeekday = (value: boolean) => updateSetting('showWeekday', value);
  const setShowDate = (value: boolean) => updateSetting('showDate', value);
  const setShowWeekNumber = (value: boolean) => updateSetting('showWeekNumber', value);
  const setUse12Hours = (value: boolean) => updateSetting('use12Hours', value);
  const setTextColor = (value: string) => updateSetting('textColor', value);
  const setFontSize = (value: string) => updateSetting('fontSize', value);
  const setFontFamily = (value: string) => updateSetting('fontFamily', value);
  const setPosition = (value: { x: number; y: number }) => updateSetting('position', value);
  const setBackgroundColor = (value: string) => updateSetting('backgroundColor', value);
  const setBackgroundImage = (value: string) => updateSetting('backgroundImage', value);

  // Apply background styles to the entire page
  useEffect(() => {
    if (!isLoaded || typeof document === 'undefined') return;

    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // Apply background styles
    if (backgroundImage) {
      console.log("Setting background image:", backgroundImage);
      htmlElement.style.backgroundImage = `url("${backgroundImage}")`;
      bodyElement.style.backgroundImage = `url("${backgroundImage}")`;
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      console.log("Setting background color:", backgroundColor);
      htmlElement.style.backgroundColor = backgroundColor;
      bodyElement.style.backgroundColor = backgroundColor;
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
    } else {
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    }

    // Common background properties
    [htmlElement, bodyElement].forEach(el => {
      el.style.backgroundSize = 'cover';
      el.style.backgroundPosition = 'center';
      el.style.backgroundRepeat = 'no-repeat';
      el.style.backgroundAttachment = 'fixed';
    });

    return () => {
      // Cleanup on unmount
      [htmlElement, bodyElement].forEach(el => {
        el.style.backgroundImage = '';
        el.style.backgroundColor = '';
      });
    };
  }, [backgroundColor, backgroundImage, isLoaded]);

  // Create inline style for the background
  const mainStyle = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative' as 'relative',
    backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
    backgroundColor: backgroundColor || 'transparent',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'fixed',
  };

  // Don't render content until settings are loaded to prevent flash of default values
  if (!isLoaded) {
    return (
      <MainLayout>
        <div className="w-full flex items-center justify-center">
          <div className="text-lg">Loading...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <StructuredData type="homepage" />
      {isFullScreen ? (
        // Full screen layout - settings button in top-right corner
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              ...mainStyle,
              minHeight: '100vh', // Full viewport height
            }}
          >
            <DigitalClock
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - floating in top-right corner for fullscreen */}
          <div className="fixed top-4 right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>
        </>
      ) : (
        // Non-fullscreen layout - full width clock with floating settings
        <>
          <div
            className="w-full flex items-center justify-center px-4 py-8 md:py-4"
            style={{
              ...mainStyle,
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            <DigitalClock
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 md:top-20 md:bottom-auto md:right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>

          {/* SEO Content - only visible in non-fullscreen mode */}
          <SEOContent isVisible={!isFullScreen} />
        </>
      )}

      {/* Debug info - only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-2 left-2 text-xs opacity-75 bg-black bg-opacity-50 text-white p-2 rounded z-40">
          <div>Settings loaded: {isLoaded ? 'Yes' : 'No'}</div>
          <div>Fullscreen: {isFullScreen ? 'Yes' : 'No'}</div>
          <div>Background: {backgroundImage ? `Image: ${backgroundImage.substring(0, 30)}...` :
           backgroundColor ? `Color: ${backgroundColor}` : 'None'}</div>
          <div>Text Color: {textColor}</div>
          <div>Font Size: {fontSize}</div>
        </div>
      )}
    </MainLayout>
  );
}
